'use client';

import Input from '@/shared/components/form/input/InputField';
import FancyLoader from '@/shared/components/ui/fancy-loader/FancyLoader';
import { useDebounce } from '@/shared/hooks/useDebounce';
import { MagnifyingGlassIcon } from '@/shared/icons';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useFrameworkFilters } from '../hooks/useFrameworkFilters';

type SearchBoxType = {
  placeholder?: string;
  isLoading?: boolean;
};

export function SearchBox({ placeholder, isLoading = false }: SearchBoxType) {
  const { searchValue, updateFilter } = useFrameworkFilters();

  // Track if we're handling an update from user input
  const isUserInput = useRef(false);

  // Track the previous search value to detect external changes
  const prevSearchValue = useRef(searchValue);

  // Local state for the input value
  const [inputValue, setInputValue] = useState(searchValue);

  // Debounced value for API calls
  const debouncedValue = useDebounce(inputValue, 500);

  // Update the filter when debounced value changes (only from user input)
  useEffect(() => {
    if (isUserInput.current && debouncedValue !== searchValue) {
      updateFilter(debouncedValue?.trim());
    }
    isUserInput.current = false;
  }, [debouncedValue, searchValue, updateFilter]);

  // Sync input value with external search value changes
  useEffect(() => {
    if (prevSearchValue.current !== searchValue && !isUserInput.current) {
      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setInputValue(searchValue);
    }
    prevSearchValue.current = searchValue;
  }, [searchValue]);

  // Handle input change
  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    isUserInput.current = true;
    setInputValue(value);
  }, []);

  return (
    <div className="flex-grow text-right relative">
      <Input
        type="text"
        id="search"
        placeholder={placeholder}
        value={inputValue}
        onChange={handleSearchChange}
        className="pr-7"
        maxLength={255}
      />

      <MagnifyingGlassIcon className="h-5 w-5 absolute right-2 top-1/2 transform -translate-y-1/2" />

      {isLoading && (
        <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
          <FancyLoader />
        </div>
      )}
    </div>
  );
}
