'use client';
import Input from '@/shared/components/form/input/InputField';
import Label from '@/shared/components/form/Label';
import { Button } from '@/shared/components/ui/button';
import { Modal } from '@/shared/components/ui/modal';
import TableTemplates from '../table-list/TableTemplate';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useTemplateCreate } from '@/features/frameworks-templates/hooks/useTemplateCreate';
import { toast } from 'sonner';
import { useFrameworkCreate } from '@/features/frameworks-templates/hooks/useFrameworkCreate';
import type { TemplatePayload } from '@/features/frameworks-templates/types';
import { useFrameworkGetDetail } from '@/features/frameworks-templates/hooks/useFrameworkGetDetail';
import type { TableConfig, TemplateListTable } from '../table-list/TableManagement';
import { useFrameworkEdit } from '@/features/frameworks-templates/hooks/useFrameworkEdit';
import ConfirmModal from './ConfirmModal';
import { useTranslations } from 'next-intl';

type TemplateFormType = {
  isOpen: boolean;
  id?: string;
  templates?: TemplateListTable[];
  nameTemplate?: string;
  closeModal: () => void;
};

export type TemplateDataTypeRef = {
  onGetTemplate: () => any[];
  isSaveAll: () => boolean;
};

const TemplateForm: React.FC<TemplateFormType> = ({ isOpen, closeModal, id, templates, nameTemplate = '' }) => {
  const t = useTranslations('framework');

  const [researchNameType, setResearchNameType] = useState<string>('');

  const [isOpenConfirmModal, setIsOpenConfirmModal] = useState<boolean>(false);

  const templateListRef = useRef<TemplateDataTypeRef | null>(null);

  const { mutateAsync } = useTemplateCreate();

  const { data: framework } = useFrameworkGetDetail(id ?? '', nameTemplate);

  const { mutateAsync: createFramework } = useFrameworkCreate();

  const { mutateAsync: updateFramework } = useFrameworkEdit();

  const tableConfig: TableConfig[] = [
    {
      tag: 'template',
      label: t('template'),
      type: 'text',
    },
    // {
    //   tag: 'step',
    //   label: 'Step',
    // },
    {
      tag: 'file',
      label: t('questionnaire'),
      type: 'file',
    },
    {
      tag: 'reportFile',
      label: t('report'),
      type: 'file',
    },
    {
      tag: 'actions',
      label: '',
      type: 'action',
    },
  ];

  useEffect(() => {
    if (framework || nameTemplate) {
      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setResearchNameType(nameTemplate);
    }
  }, [framework, nameTemplate]);

  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setResearchNameType(e.target.value);
  }, []);

  const createPayload = (template: TemplateListTable, isUpdate?: boolean) => {
    return ({
      ...(isUpdate && { id: template.id }),
      types: [
        template.file.name && {
          [template.file.name]: template.file.mineType || template.file.type || template.file.mimetype,
          category: 'questionnaire',
        },
        template.reportFile.name && {
          [template.reportFile.name]: template.reportFile.mineType || template.reportFile.type || template.reportFile.mimetype,
          category: 'report',
        },
      ],
      fileIds: [
        template.reportFile.name
        && template.reportFile._id,

        template.file.name
        && template.file._id,

      ],
      type: 'framework',
      name: template.template,
    }
    );
  };

  const saveFramework = async () => {
    const templateList = templateListRef.current?.onGetTemplate() ?? [];

    const { templateNew, templateOld } = templateList.reduce((acc, template) => {
      const isNew = (template.id as string).includes('new');
      if (isNew) {
        acc.templateNew.push(template);
      } else {
        acc.templateOld.push(template);
      }
      return acc;
    }, { templateNew: [], templateOld: [] } as { templateNew: TemplateListTable[]; templateOld: TemplateListTable[] });

    const payloads: TemplatePayload[] = templateNew.map((template: TemplateListTable) => {
      return createPayload(template);
    });

    const oldPayload: TemplatePayload[] = templateOld.map((template: TemplateListTable) => {
      return createPayload(template, true);
    });

    await Promise.all(oldPayload.map(payload => mutateAsync(payload)));

    try {
      const response = await Promise.all(payloads.map(payload => mutateAsync(payload)));
      const payload = {
        name: researchNameType,
        templateIds: [
          ...templateOld.map((template: TemplateListTable) => template.id),
          ...response.map(res => res.data?.id ?? ''),
        ],
      };
      try {
        if (id) {
          const res = await updateFramework({ payload, id });
          toast.success(res.message);
        } else {
          const res = await createFramework(payload);

          toast.success(res.message);
        }
        setResearchNameType('');
        closeModal();
      } catch (error: any) {
        if (id) {
          toast.error(t('errorUpdateFramework'));
        } else {
          toast.error(t(error.message || 'errorCreateFramework'));
        }
      }
    } catch (error: any) {
      toast.error(t(error.message || 'errorServer'));
    }
  };

  const handleSubmit = () => {
    const isSaveAll = templateListRef.current?.isSaveAll() ?? false;

    if (isSaveAll) {
      setIsOpenConfirmModal(true);
      return;
    }

    if (!researchNameType) {
      toast.error(t('messageError'));
      return;
    }
    saveFramework();
  };

  const handleConfirmModal = () => {
    saveFramework();
    setIsOpenConfirmModal(false);
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={() => {
        setResearchNameType('');
        closeModal();
      }}
      className="max-w-[900px] min-w-[890px]"
    >
      <div className="no-scrollbar w-full p-6">
        <h4 className="mb-2 text-2xl font-medium text-gray-800 dark:text-white/90">
          {id ? t('headerEdit') : t('headerCreate')}
        </h4>
        <p className="mb-6 text-sm text-gray-500 dark:text-gray-400 lg:mb-7">
          {t('titleRequired')}
        </p>

        <div>
          <div>
            <Label htmlFor="name">
              {t('name')}
              {' '}
              <span className="text-error-500">*</span>
            </Label>

            <Input
              id="name"
              placeholder={t('name')}
              type="text"
              value={researchNameType ?? ''}
              onChange={handleSearchChange}
              maxLength={255}
            />
          </div>

          <div className="mt-3">
            <Label htmlFor="template">
              {t('fileUpload')}
            </Label>

            <div className="max-h-100 overflow-auto border-b relative">
              <TableTemplates
                ref={templateListRef}
                templateList={templates ?? []}
                tableConfig={tableConfig}
                backgroundColorClass="bg-white"
                isForm={true}
              />
            </div>
          </div>

        </div>

        <div className="flex items-center justify-end gap-4 mt-3">
          <Button
            type="button"
            variant="outline"
            onClick={() => {
              closeModal();
              setResearchNameType('');
            }}
          >
            {t('common.cancel')}
          </Button>

          <Button
            type="button"
            onClick={handleSubmit}
          >
            {id ? t('common.update') : t('common.create')}
          </Button>
        </div>
      </div>

      <ConfirmModal
        isOpen={isOpenConfirmModal}
        title={t('unsaved')}
        message={t('messageUnsaved')}
        onConfirm={handleConfirmModal}
        closeModal={() => {
          setIsOpenConfirmModal(false);
        }}
      />
    </Modal>
  );
};

export default TemplateForm;
