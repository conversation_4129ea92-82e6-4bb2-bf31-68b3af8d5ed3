'use client';

import { useQuery } from '@tanstack/react-query';
import { getFrameworkList } from '@/features/frameworks-templates/components/services/framework-template.service';
import type { FrameworkFilters } from '@/features/frameworks-templates/components/services/framework-template.service';

/**
 * Hook for fetching framework list for research templates
 *
 * This hook provides a way to fetch frameworks with search functionality
 * for use in research form template selection.
 *
 * @param filters - Search and pagination filters
 * @returns Query data and methods
 */
export function useProjectResearchFramework(filters: FrameworkFilters = {}, staleTime: number = 5 * 60 * 1000, isRetry: boolean = false) {
  const query = useQuery({
    queryKey: ['frameworks', 'research', filters],
    queryFn: async () => {
      const response = await getFrameworkList(filters);
      return response.data;
    },
    staleTime, // 5 minutes
    refetchOnMount: isRetry,
  });

  // Transform data for easier use in components
  const frameworks = query.data?.items || [];

  // Create options for select component with 'Other' option at the end
  const frameworkOptions = [
    ...frameworks.map(framework => ({
      value: framework,
      label: framework.name,
    })),
    {
      value: 'other',
      label: 'Other',
    },
  ];

  return {
    // Query state
    frameworks,
    frameworkOptions,
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,

    // Query methods
    refetch: query.refetch,

    // Pagination info
    total: query.data?.total || 0,
    page: query.data?.page || 1,
    itemsPerPage: query.data?.itemsPerPage || 10,
    totalPages: query.data?.totalPages || 1,
    hasNextPage: query.data?.hasNextPage || false,
    hasPreviousPage: query.data?.hasPreviousPage || false,
  };
}
