'use client';

import { useQuery } from '@tanstack/react-query';
import { getResearch } from '../services/project.service';
import { useCallback, useMemo, useState } from 'react';

export function useResearchForDocumentReport(stepId: string) {
  const [searchQuery, setSearchQuery] = useState('');

  const { data, isLoading, error } = useQuery({
    queryKey: ['research', searchQuery],
    queryFn: () => getResearch({
      searchValue: searchQuery,
      itemsPerPage: 50,
      stepId,
      mark: true,
    }),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const researchOptions = useMemo(() => {
    if (!data?.data?.items) {
      return [];
    }

    return data.data.items.map(research => ({
      value: research.id,
      text: research.name,
      selected: false,
    }));
  }, [data]);

  // Handle search input changes
  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);
  }, []);

  return {
    researchOptions,
    isLoading,
    error,
    searchQuery,
    handleSearch,
  };
}
