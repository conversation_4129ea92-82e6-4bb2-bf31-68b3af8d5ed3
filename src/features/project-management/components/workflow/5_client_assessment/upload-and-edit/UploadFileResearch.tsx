import { Button } from '@/shared/components/ui/button';
import { BoxCubeIcon } from '@/shared/icons';
import FileUpload from '../../initial-screening-form/FileUpload';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import type { IFileResponse } from '@/shared/types/global';
import { toast } from 'sonner';
import { useProjectUpdateQuestionAnswer } from '@/features/project-management/hooks/useProjectUpdateQuestionAnswer';
import { useDirty } from '@/features/project-management/contexts/DirtyStepContext';
import { compareObjectArray } from '@/shared/utils/compareObject';
import { GuardConfirmationModal } from '@/shared/components/modals/GuardConfirmationModal';
import { useRouteGuardWithDialog } from '@/shared/hooks/route-guard/use-route-guard-with-dialog';
import { useUpdateStatusStep } from '@/features/project-management/hooks/useUpdateStatusStep';
import { EDeskResearch } from '@/features/project-management/types/questionnaire';
import { EStatusTask } from '@/features/project-management/types/workflow';
import { EValueModelAI } from '@/features/project-management/constants/modelAI';
import SelectModelAI from '../../common/SelectModelAI';
import { useTranslations } from 'next-intl';
import { debounce } from '@/shared/utils/debounce';

type UploadFileResearchType = {
  showViewButton?: boolean;
  data: any[];
  initialData: any[];
  id: string;
  type: number;
  model: string;
  stepId: string;
  changeNextView: ((type: 'upload' | 'editor') => void);
  onBackDashboard: () => void;
  onViewData: () => void;
};

const UploadFileResearch: React.FC<UploadFileResearchType> = ({
  showViewButton,
  data,
  id,
  type,
  stepId,
  model,
  initialData,
  changeNextView,
  onBackDashboard,
  onViewData,
}) => {
  const t = useTranslations('workflow');

  const [initialFile, _setInitialFile] = useState<IFileResponse[]>(() => data.length ? data[0].files : []);

  const [files, setFiles] = useState<IFileResponse[]>(() => data.length ? data[0].files : []);

  const [isSaved, _setIsSaved] = useState(true);

  const [isShowModal, setIsShowModal] = useState(false);

  const [modelAIDefault, setModelAIDefault] = useState<string>(EValueModelAI.GPT);

  const [modelAISelected, setModelAISelected] = useState<string>(EValueModelAI.GPT);

  const titleConfirm = t('common.titleConfirmChange');

  const descriptionConfirm = t('common.descriptionConfirm');

  const { registerStep, clearStep } = useDirty();

  const isSubmittingRef = useRef<boolean>(false);

  const { showDialog, title, message, onConfirm, onCancel } = useRouteGuardWithDialog({
    when: !isSaved,
    title: t('common.titleUnSave'),
    message: t('common.descriptionGuard'),
  });

  const { updateQuestionAnswer } = useProjectUpdateQuestionAnswer();

  const { mutateAsync: updateStatusStepData } = useUpdateStatusStep();

  const handleFilesChange = React.useCallback((uploadedFiles: IFileResponse[]) => {
    setFiles(uploadedFiles);

    const isChanged = !compareObjectArray(initialFile, uploadedFiles);
    const isChangedModel = modelAIDefault !== modelAISelected;

    _setIsSaved(!isChanged || isChangedModel);

    registerStep(stepId, () => isChanged || !isChangedModel);
  }, []);

  useEffect(() => {
    // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
    setModelAISelected(model);
    // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
    setModelAIDefault(model);
  }, [model]);

  const isChanged = useMemo(() => {
    const isChangedModel = modelAIDefault !== modelAISelected;
    return !compareObjectArray(initialFile, files) || isChangedModel;
  }, [initialFile, files, modelAIDefault, modelAISelected]);

  const saveStepInfos = async () => {
    clearStep(stepId);
    const payload = {
      formStepId: id,
      stepInfos: [
        {
          order: 0,
          type,
          infos: [{
            files: files.map(file => ({
              ...file,
              file: file.key,
              name: file.originalname,
              type: file.mimeType,
              id: file._id,
            })),
          }],
          model: modelAISelected,
        },
      ],
    };

    await updateQuestionAnswer(payload, stepId);

    setTimeout(() => {
      changeNextView('editor');
    });
  };

  const updateStatusStep = async () => {
    const ids = initialData.filter(data => data.type !== EDeskResearch.UPLOAD_FILE).map(t => t.id);

    await updateStatusStepData({
      id: stepId,
      status: EStatusTask.COMPLETED,
      stepIds: [],
      stepInfoIds: ids,
      select: 'all',
      isGenerate: true,
    });
  };

  const resetFile = () => {
    setFiles(initialFile);
    onViewData();
  };

  const handleConfirmPopUp = async () => {
    await updateStatusStep();
    await saveStepInfos();
  };

  const handleCancelPopUp = () => {
    setIsShowModal(false);
  };

  const handleGenerateData = debounce(async () => {
    if (!files.length) {
      toast.error(t('deskResearch.errorNextStep'));
      return;
    }

    if (!isChanged) {
      onViewData();
      return;
    }

    if ((isChanged) && initialFile.length) {
      setIsShowModal(true);
      return;
    }

    if (isSubmittingRef.current) {
      return;
    }

    isSubmittingRef.current = true;

    await saveStepInfos();
  }, 300);

  const handleChangeModelAI = (data: string) => {
    setModelAISelected(data);
  };

  return (
    <>
      <div className="flex items-center justify-between gap-3">
        <div className="flex flex-col gap-2">
          <h2 className="text-xl font-medium text-foreground mb-0">
            {t('deskResearch.title')}
          </h2>

          <p className="mb-0">{t('deskResearch.description')}</p>

        </div>

        <div className="flex items-center gap-2">

          <SelectModelAI
            onChangeModel={handleChangeModelAI}
            defaultValue={modelAIDefault}
            disable={false}
            top="top-0"
            position="unset"

          />

          <Button
            variant="secondary"
            onClick={onBackDashboard}
          >
            <BoxCubeIcon className="h-5 w-5" />
            {t('common.backToDashboard')}
          </Button>
        </div>
      </div>

      <div className="my-2">
        {t('common.titleFiles')}
      </div>
      <FileUpload onFilesChange={handleFilesChange} initialFile={initialFile} background="bg-gray-100" />

      <div className={`sticky bottom-0 left-0 right-0  border-border p-4 z-20 flex justify-center gap-4 `}>

        {showViewButton && (
          <Button
            type="button"
            variant="outline"
            onClick={resetFile}
          >
            {t('common.viewResearch')}
          </Button>
        )}

        <Button
          type="button"
          onClick={handleGenerateData}
        >
          {t('common.generate')}
        </Button>
      </div>

      {/* Modal for confirm content */}
      <GuardConfirmationModal
        open={isShowModal}
        onOpenChange={() => {}}
        title={titleConfirm}
        description={descriptionConfirm}
        onConfirm={() => handleConfirmPopUp()}
        onCancel={() => handleCancelPopUp()}
        confirmText={t('common.continue')}
        cancelText={t('common.cancel')}
      />

      {/* Modal for guard */}
      <GuardConfirmationModal
        open={showDialog}
        onOpenChange={() => {}}
        title={title}
        description={message}
        onConfirm={onConfirm}
        onCancel={onCancel}
      />
    </>

  );
};

export default UploadFileResearch;
