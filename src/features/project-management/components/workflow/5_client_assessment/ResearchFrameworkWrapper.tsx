import QuestionFormWrapper from './question-form/QuestionFormWrapper';
import { EFrameworkType } from '@/features/project-management/types/questionnaire';
import type { ConversationDataType } from '@/features/project-management/types/questionnaire';
import { useEffect, useMemo, useState } from 'react';
import { CONVERT_TYPE_RESEARCH_TO_NAME } from '@/features/project-management/constants/questionnaire';
import UploadAndEditWrapper from './upload-and-edit/UploadAndEditWrapper';
import { useGetInfoDetail } from '@/features/project-management/hooks';
import ProjectCardSkeleton from '../../project-list/ProjectCardSkeleton';
import type { IFileResponse } from '@/shared/types/global';
import QualitativeQuestionnaireWrapper from './quality-flow/QualitativeQuestionnaireWrapper';
import { Modal } from '@/shared/components/ui/modal';
import { useModal } from '@/shared/hooks/useModal';
import { <PERSON>down<PERSON>enderer } from '@/shared/components/ui/markdown/MarkdownRenderer';
import { useTranslations } from 'next-intl';

type ResearchFrameworkType = {
  type: EFrameworkType;
  data: any[];
  id: string;
  stepId: string;
  templates: IFileResponse[];
  nameTemplate: string;
  evaluationFramework: string;
  nameForm: string;
  conversationData: ConversationDataType[];
  onBackDashboard: () => void;
};

const ResearchFrameworkWrapper: React.FC<ResearchFrameworkType> = ({
  type,
  data,
  id,
  stepId,
  templates,
  nameTemplate,
  nameForm,
  evaluationFramework,
  conversationData,
  onBackDashboard,
}) => {
  const t = useTranslations('workflow');

  const { isOpen, openModal, closeModal } = useModal();

  const [scoringData, setScoringData] = useState<string>('');

  const getNameTypeSearch = useMemo(() => {
    return t(`research.${CONVERT_TYPE_RESEARCH_TO_NAME[type]}`);
  }, [type, t]);

  const getStatusData = useMemo(() => {
    return data.length ? 'editor' : 'upload';
  }, [data]);

  const handleOpenDetailScore = (data: string) => {
    setScoringData(data);
    openModal();
  };

  return (
    <>
      <div className="sticky top-0 z-100 bg-white flex items-center justify-between p-4 md:p-6 pb-2! border-b border-gray-200 dark:bg-black dark:text-white">
        <h6 className="pb-0!">
          {nameTemplate}
        </h6>

        <ul>
          <li className="pb-0! text-sm text-black list-disc dark:text-white">{getNameTypeSearch}</li>
        </ul>

      </div>
      <div className="p-4 md:p-6">
        {(type === EFrameworkType.DESK_RESEARCH || type === EFrameworkType.OTHER)
          ? (
              <UploadAndEditWrapper
                status={getStatusData}
                data={data}
                id={id}
                stepId={stepId}
                templates={templates}
                evaluationFramework={evaluationFramework}
                conversationData={conversationData}
                nameForm={nameForm}
                onBackDashboard={onBackDashboard}
                onOpenDetailScore={handleOpenDetailScore}
              />
            )
          : type === EFrameworkType.QUANTITATIVE
            ? (
                <QuestionFormWrapper
                  data={data}
                  stepId={stepId}
                  id={id}
                  templates={templates}
                  evaluationFramework={evaluationFramework}
                  conversationData={conversationData}
                  nameForm={nameForm}
                  onBackDashboard={onBackDashboard}
                  onOpenDetailScore={handleOpenDetailScore}
                />
              )
            : (
                <QualitativeQuestionnaireWrapper
                  data={data}
                  stepId={stepId}
                  id={id}
                  nameForm={nameForm}
                  templates={templates}
                  conversationData={conversationData}
                  evaluationFramework={evaluationFramework}
                  onBackDashboard={onBackDashboard}
                  onOpenDetailScore={handleOpenDetailScore}
                />
              )}
      </div>

      <Modal
        showCloseButton={false}
        isOpen={isOpen}
        onClose={closeModal}
        className="overflow-auto
      w-[980px] max-h-[700px]"
      >
        <div className="no-scrollbar w-full p-6 h-full">
          <div className="overflow-auto h-full"><MarkdownRenderer content={scoringData} /></div>
        </div>
      </Modal>
    </>
  );
};

// export default ResearchFrameworkWrapper;
type ResearchDetailType = {
  id: string;
  type: EFrameworkType | null;
  stepFormId: string;
  nameTemplate: string;
  onBackDashboard: () => void;
};
const ResearchDetailView: React.FC<ResearchDetailType> = ({ id, nameTemplate, stepFormId, type, onBackDashboard }) => {
  const [dataFormStep, setDataFormStep] = useState<any | null>(null);

  const [data, setData] = useState<any[]>([]);

  const [nameForm, setNameForm] = useState<string>('');

  const [conversationData, setConversationData] = useState<ConversationDataType[]>([]);

  const [evaluationFramework, setEvaluationFramework] = useState<string>('');

  const { data: dataStep } = useGetInfoDetail<any, any>(id);

  useEffect(() => {
    if (type === null) {
      return;
    }
    if (stepFormId === null) {
      return;
    }
    if (dataStep && dataStep.formStep.length) {
      const data = dataStep.formStep.find(t => t.infos[0]?.researchType === type && t.id === stepFormId);

      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setNameForm(data?.name);

      if (data) {
        const dataStepInfo = dataStep.stepInfo.filter((step: any) => step.formStepId === data.id);

        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setData(dataStepInfo);
      }
      const raw = data?.infos?.[0]?.raw;
      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setEvaluationFramework(raw ?? '');

      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setDataFormStep(data ?? []);

      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setConversationData(data?.conversation?.map((d: ConversationDataType) => ({ id: d.id, order: d.order })));
    }
  }, [dataStep, type, stepFormId]);

  if (!dataFormStep) {
    return (
      <div className="p-4 md:p-6">
        <ProjectCardSkeleton />
      </div>
    );
  }

  return (
    <>
      <ResearchFrameworkWrapper
        type={dataFormStep?.infos?.[0]?.researchType as EFrameworkType || 0}
        id={dataFormStep.id}
        templates={dataFormStep?.infos?.[0]?.template || []}
        data={data}
        conversationData={conversationData}
        evaluationFramework={evaluationFramework}
        stepId={id}
        nameForm={nameForm}
        nameTemplate={nameTemplate}
        onBackDashboard={onBackDashboard}
      />

    </>
  );
};

export default ResearchDetailView;
