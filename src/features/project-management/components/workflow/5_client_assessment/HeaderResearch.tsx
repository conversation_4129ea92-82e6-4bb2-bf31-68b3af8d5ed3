import type { OptionChangeViewType } from '@/features/project-management/types/questionnaire';
import { Button } from '@/shared/components/ui/button';
import { AngleLeftIcon, ArrowDownTrayIcon, BoxCubeIcon, CheckIcon, CircleCheckIcon, FileEditIcon, LinkIcon } from '@/shared/icons';
import { useEffect, useState } from 'react';
import ScoringOverview from './ScoringOverview';
import type { ScoringReportDataType } from '@/features/project-management/types/project';
import SelectModelAI from '../common/SelectModelAI';
import { EValueModelAI } from '@/features/project-management/constants/modelAI';
import { useTranslations } from 'next-intl';
import { useChatBoxVisible } from '@/features/project-management/stores/chatbox-store';

type HeaderResearchType = {
  options: OptionChangeViewType[];
  selectedType: string;
  textBack?: string;
  header?: string;
  description?: string;
  textCopy: string;
  isViewCopyBox?: boolean;
  isHiddenBackButton?: boolean;
  isNotAcceptEdit?: boolean;
  isLoading?: boolean;
  isScoringAI?: boolean;
  dataScoring?: ScoringReportDataType | null;
  isShowButtonReGen?: boolean;
  isShowDownloadIcon?: boolean;
  modelAIDefault?: string;
  isShowModel?: boolean;
  styleButton?: { [key: string]: string };
  labelSaveAndNext?: string;
  handleSelectedType: (o: OptionChangeViewType) => void;
  onClickApprove: () => void;
  onSaveAndNextStep: () => void;
  onBackDashboard: () => void;
  onBackUploadFile: () => void;
  onEditData?: (status: boolean) => void;
  onOpenDetailScore?: (data: string) => void;
  onReGen?: () => void;
  onDownloadFile?: () => void;
  onChangeModel?: (data: string) => void;
};

const HeaderResearch: React.FC<HeaderResearchType> = ({
  isHiddenBackButton = false,
  options,
  selectedType,
  isViewCopyBox,
  isNotAcceptEdit,
  textCopy,
  textBack,
  header,
  description,
  isLoading,
  isScoringAI,
  dataScoring,
  isShowButtonReGen = false,
  isShowDownloadIcon = false,
  modelAIDefault = EValueModelAI.GPT,
  isShowModel = true,
  styleButton,
  labelSaveAndNext,
  handleSelectedType,
  onClickApprove,
  onSaveAndNextStep,
  onBackDashboard,
  onBackUploadFile,
  onEditData,
  onOpenDetailScore,
  onReGen,
  onDownloadFile,
  onChangeModel,
}) => {
  const isVisible = useChatBoxVisible();

  const t = useTranslations('workflow');

  const [isCopied, setIsCopied] = useState<boolean>(false);

  const score = dataScoring?.score ?? 0;

  useEffect(() => {
    if (!isCopied) {
      return;
    }
    // eslint-disable-next-line react-web-api/no-leaked-timeout
    setTimeout(() => {
      setIsCopied(false);
    }, 5000);
  }, [isCopied]);

  const onCopyText = async () => {
    if (isCopied) {
      return;
    }

    await navigator.clipboard.writeText(textCopy);
    setIsCopied(true);
  };

  const handleChangeEditMode = () => {
    if (onEditData) {
      onEditData(true);
    }
  };

  const handleClickScoring = () => {
    if (onOpenDetailScore && dataScoring) {
      onOpenDetailScore(dataScoring.report);
    }
  };

  const handleReGen = () => {
    if (onReGen) {
      onReGen();
    }
  };

  const handleDownloadFile = () => {
    if (onDownloadFile) {
      onDownloadFile();
    }
  };

  const handleChangeModelAI = (model: string) => {
    if (onChangeModel) {
      onChangeModel(model);
    }
  };

  return (
    <>
      <div className="flex items-center justify-between gap-3">
        <div className="flex flex-col gap-2">
          <h2 className="text-xl font-medium mb-0 text-green-600">
            {header ?? t('common.headerDefault')}
          </h2>

          <p className="mb-0">{description ?? t('common.description')}</p>

        </div>

        <div className="flex items-center gap-2">
          {
            isScoringAI && (
              <ScoringOverview score={score} openModal={handleClickScoring} />
            )
          }

          {/* {isShowButtonReGen && (
            <Button onClick={handleReGen} type="button" variant="outline" className="text-cyan-500 bg-cyan-50">
              <RefreshCcw className="h-5 w-5 " />
            </Button>
          )} */}

          <Button
            variant="secondary"
            onClick={onBackDashboard}
          >
            <BoxCubeIcon className="h-5 w-5" />
            {t('common.backToDashboard')}
          </Button>

          {isShowDownloadIcon && (
            <Button
              type="button"
              variant="outline"
              onClick={handleDownloadFile}
            >
              <ArrowDownTrayIcon className="h-5 w-5 " />
            </Button>
          )}

          {isNotAcceptEdit
            ? (
                !isLoading && (
                  <>
                    <Button onClick={handleChangeEditMode} variant="secondary">
                      <FileEditIcon className="h-5 w-5" />
                      {t('common.edit')}

                    </Button>
                    <Button onClick={onClickApprove} variant="default">
                      <CircleCheckIcon className="h-5 w-5" />
                      {t('common.approve')}

                    </Button>
                  </>
                )
              )
            : (
                <Button
                  onClick={onSaveAndNextStep}
                  variant="default"
                  style={styleButton || {}}
                >
                  <CircleCheckIcon className="h-5 w-5" />
                  {labelSaveAndNext ?? t('common.saveNext')}
                </Button>
              )}

        </div>
      </div>

      <div className="flex items-center justify-between mt-4">
        <div className="flex p-1 bg-gray-100 rounded-lg w-fit">
          {options.map(option => (
            <button
              key={option.id}
              type="button"
              onClick={() => handleSelectedType(option)}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${selectedType === option.id
                ? 'bg-white text-black shadow-sm'
                : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              {option.label}
            </button>
          ))}
        </div>

        <div className="flex items-center gap-2">

          {(isShowModel && !isVisible) && (
            <SelectModelAI
              onChangeModel={handleChangeModelAI}
              defaultValue={modelAIDefault}
              isShowReGenButton={isShowButtonReGen}
              onReGen={handleReGen}
              disable={false}
              top="top-0"
              position="unset"
            />
          )}

          {!isHiddenBackButton && (
            <div onClick={onBackUploadFile} className="flex items-center gap-2 p-2 border text-sm border-gray-100 rounded-md cursor-pointer hover:bg-gray-300">
              <AngleLeftIcon className="h-4 w-4" />
              {textBack ?? t('common.backToUploadFile')}
            </div>
          )}
        </div>
      </div>

      {isViewCopyBox && (
        <div className=" mt-2 flex items-center justify-between p-4 border border-green-500 bg-green-50 rounded-md">
          <div>
            <h4 className="text-green-600 font-semibold text-lg">
              {' '}
              {t('common.readyCopy')}
            </h4>
            <p className="text-green-500 text-sm">
              {t('common.descriptionCopy')}

            </p>
          </div>
          <Button
            onClick={onCopyText}
            className="flex items-center gap-2 bg-green-500 text-white px-4 py-2 rounded-md hover:bg-green-600"
          >
            {isCopied
              ? (
                  <>
                    <CheckIcon className="w-5 h-5" />
                    {t('common.copied')}

                  </>
                )
              : (
                  <>
                    <LinkIcon className="w-5 h-5" />
                    {t('common.copyLink')}

                  </>
                )}
          </Button>
        </div>
      )}
    </>

  );
};

export default HeaderResearch;
