'use client';
import { useTranslations } from 'next-intl';

const MessageWarning: React.FC = () => {
  const t = useTranslations('workflow');
  return (
    <>
      <div className="mt-4 p-2 h-full w-full flex items-center justify-center bg-red-50 dark:bg-black ">
        <p className="text-sm m-0 text-red-500">
          {t('common.message')}
        </p>
      </div>
    </>
  );
};

export default MessageWarning;
