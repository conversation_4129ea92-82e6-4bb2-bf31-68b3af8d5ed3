'use client';

import React, { useEffect, useMemo, useRef, useState } from 'react';
import ProjectCardSkeleton from '../../project-list/ProjectCardSkeleton';
import WorkflowNavigation from '../layout/WorkflowNavigation';
import { useCurrentStep, useCurrentTask, useWorkflowActions } from '@/features/project-management/stores/project-workflow-store';
import { MarkdownRenderer } from '@/shared/components/ui/markdown/MarkdownRenderer';
import { useGetInfoDetail } from '@/features/project-management/hooks';
import type { StepInfosPayload } from '@/features/project-management/types/evaluation';
import { useProjectUpdateQuestionAnswer } from '@/features/project-management/hooks/useProjectUpdateQuestionAnswer';
import { useUpdateStatusStep } from '@/features/project-management/hooks/useUpdateStatusStep';
import { EStatusTask } from '@/features/project-management/types/workflow';
import { useCoAgent } from '@copilotkit/react-core';
import type { stateRouteAgent } from '@/shared/types/global';
import { AGENT_ROUTE_NAME } from '@/shared/constants/global';
import type { scopeOfWorkFlow } from '@/features/project-management/types/agent';
import { EValueModelAI } from '@/features/project-management/constants/modelAI';
import SelectModelAI from '../common/SelectModelAI';
import { GuardConfirmationModal } from '@/shared/components/modals/GuardConfirmationModal';
import { useQueryClient } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';
import { useChatBoxVisible } from '@/features/project-management/stores/chatbox-store';
import { debounce } from '@/shared/utils/debounce';

const ProjectScopingReviewInput: React.FC = () => {
  const t = useTranslations('workflow');

  const isVisible = useChatBoxVisible();

  const [markdown, setMarkdown] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const [isViewBrief, setIsViewBrief] = useState<boolean>(false);

  const currentStep = useCurrentStep();
  const currentStepId = currentStep?.id;

  const currentTask = useCurrentTask();

  const {
    completeStep,
    getNextStepId,
    updateStatus,
  } = useWorkflowActions();

  const { mutateAsync } = useUpdateStatusStep();

  const { data } = useGetInfoDetail<any, any>(currentStepId ?? '');

  const [modelAIDefault, setModelAIDefault] = useState<string>(EValueModelAI.GPT);

  const [modelAISelected, setModelAISelected] = useState<string>(EValueModelAI.GPT);

  const [isShowModal, setIsShowModal] = useState(false);

  const titleConfirm = t('common.titleConfirmChange');

  const descriptionConfirm = t('common.descriptionConfirm');

  const { updateQuestionAnswer } = useProjectUpdateQuestionAnswer();

  const queryClient = useQueryClient();

  const isSubmittingRef = useRef<boolean>(false);

  const updateMarkdownToState = (data: string) => {
    const updateData = () => {
      setMarkdown(data);
      setIsLoading(false);
    };

    updateData();
    return true;
  };

  // AI agent hooks
  // const { appendMessage } = useCopilotChat();
  const { setState: _setCoAgentsState } = useCoAgent<stateRouteAgent<scopeOfWorkFlow>>({
    name: AGENT_ROUTE_NAME,
    initialState: {
    },
  });

  // const handleSendPrompt = React.useCallback((markdown: string) => {
  //   setCoAgentsState((prevState: any) => ({
  //     ...prevState,
  //     agent_name: AGENT_NAME_COPILOTKIT.SCOPE,

  //     [ENameStateAgentCopilotkit.SCOPE]: {
  //       ...prevState[ENameStateAgentCopilotkit.SCOPE],
  //       brief_analysis: markdown,
  //     },
  //   }));

  //   appendMessage(
  //     new TextMessage({
  //       content: MESSAGE_SEND_ROUTE_AGENT,
  //       role: Role.Developer,
  //     }),
  //   );
  // }, [setCoAgentsState, appendMessage]);

  useEffect(() => {
    if (data?.stepInfoPrevious.length && data?.stepInfoPrevious[0]?.infos?.length && data?.stepInfoPrevious[0]?.infos[0]?.value) {
      updateMarkdownToState(data?.stepInfoPrevious[0]?.infos[0]?.value);
    }

    if (data?.stepInfo.length && data?.stepInfo[0]?.infos.length) {
      const model = data?.stepInfo[0].model ?? EValueModelAI.GPT;
      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setModelAIDefault(model);
      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setModelAISelected(model);
    }

    if (data?.stepInfoNext.length && data?.stepInfoNext[0]?.infos?.length && data?.stepInfoNext[0]?.infos[0]?.value) {
      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setIsViewBrief(true);
    } else {
      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setIsViewBrief(false);
    }
  }, [data]);

  const isChangedModel = useMemo(() => {
    return modelAIDefault !== modelAISelected;
  }, [modelAIDefault, modelAISelected]);

  const saveDataInDB = async () => {
    if (!currentStepId) {
      return;
    }

    const payload: StepInfosPayload = {
      stepInfos: [
        {
          order: 0,
          infos: [{ value: markdown }],
          model: modelAISelected,
        },
      ],
    };

    await updateQuestionAnswer(payload, currentStepId);
  };

  const handleSubmit = debounce(async () => {
    if (!currentStepId) {
      return;
    }

    if (isChangedModel && currentStep.status === EStatusTask.COMPLETED) {
      setIsShowModal(true);
      return;
    }

    if (isSubmittingRef.current) {
      return;
    }

    isSubmittingRef.current = true;

    // handleSendPrompt(markdown);
    if (currentStep.status !== EStatusTask.COMPLETED) {
      await saveDataInDB();
      mutateAsync({ id: currentTask?.id ?? '', status: EStatusTask.IN_PROGRESS });

      mutateAsync({ id: currentStep?.id ?? '', status: EStatusTask.COMPLETED });
    }

    completeStep(currentStepId);
  }, 300);

  const handleChangeModelAI = (data: string) => {
    setModelAISelected(data);
  };

  const handleCancelPopUp = () => {
    setIsShowModal(false);
  };

  const handleConfirmPopUp = debounce(async () => {
    if (!currentStepId || isSubmittingRef.current) {
      return;
    }
    isSubmittingRef.current = true;

    const nextStepId = getNextStepId();
    await mutateAsync({
      id: currentStepId,
      status: EStatusTask.IN_PROGRESS,
      select: 'all',
      isGenerate: true,
      stepIds: [nextStepId],
      stepInfoIds: [],
      stepInfoIdsGenerate: [],
    });
    await saveDataInDB();

    await queryClient.invalidateQueries({ queryKey: ['getInfoDetail', nextStepId], type: 'all' });

    updateStatus(currentTask?.id ?? '', EStatusTask.IN_PROGRESS, true);
    updateStatus(nextStepId, EStatusTask.IN_PROGRESS);
    setIsShowModal(false);

    completeStep(currentStepId);
  }, 300);

  return isLoading
    ? (
        <div className="p-4 md:p-6 ">
          <div className="mb-1 md:mb-2">{t('common.loading')}</div>
          <ProjectCardSkeleton />
        </div>
      )
    : (
        <div className="p-4 md:p-6">

          {!isVisible && (
            <SelectModelAI
              onChangeModel={handleChangeModelAI}
              defaultValue={modelAIDefault}
              disable={false}
              top="top-60"
              right="right-14"
            />
          )}

          <div className="flex items-center gap-1.5 justify-end sticky mt-[-60px] top-4 right-4 md:right-4 md:top-6">

            <WorkflowNavigation
              onComplete={handleSubmit}
              nextButtonText={isViewBrief ? t('common.nextStep') : t('common.generate')}
              showPrevious={true}
              prevButtonText={t('common.back')}
              panelClass=""
            />
          </div>

          <div className="">

            <MarkdownRenderer content={markdown} />
          </div>

          {/* Modal for confirm content */}
          <GuardConfirmationModal
            open={isShowModal}
            onOpenChange={() => {}}
            title={titleConfirm}
            description={descriptionConfirm}
            onConfirm={() => handleConfirmPopUp()}
            onCancel={() => handleCancelPopUp()}
            confirmText={t('common.continue')}
            cancelText={t('common.cancel')}
          />
        </div>
      );
};

export default ProjectScopingReviewInput;
