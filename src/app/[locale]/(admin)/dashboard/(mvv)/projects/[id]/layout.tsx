import { ProjectWorkflowStoreProvider } from '@/features/project-management/stores/project-workflow-store';
import { CopilotKit } from '@copilotkit/react-core';
import '@copilotkit/react-ui/styles.css';

export const metadata = {
  title: 'Minastik | Project Detail',
  description: 'Manage your project detail with Minastik',
};

export default async function ProjectDetailLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;

  return (
    <CopilotKit runtimeUrl="/api/ai-chat">
      <ProjectWorkflowStoreProvider projectId={id}>
        {children}
      </ProjectWorkflowStoreProvider>
    </CopilotKit>
  );
}
