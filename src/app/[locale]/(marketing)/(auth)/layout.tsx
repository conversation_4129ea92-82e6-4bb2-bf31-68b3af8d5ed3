import type { VariantProps } from 'class-variance-authority';
import Image from 'next/image';
import Link from 'next/link';
import React from 'react';
import GridShape from '@/shared/components/common/GridShape';
import { ThemeProvider } from '@/shared/contexts/ThemeContext';
import {
  authLayoutContainerVariants,
  authLayoutSidebarVariants,
  authLayoutTextVariants,
  authLayoutWrapperVariants,
} from './auth-layout-variants';

type AuthLayoutProps = {
  containerVariant?: VariantProps<typeof authLayoutContainerVariants>['variant'];
  wrapperVariant?: VariantProps<typeof authLayoutWrapperVariants>['variant'];
  sidebarVariant?: VariantProps<typeof authLayoutSidebarVariants>['variant'];
  textVariant?: VariantProps<typeof authLayoutTextVariants>['variant'];
};

/**
 * AuthLayout Component
 *
 * This component uses semantic color tokens from our theming system.
 * It provides a layout for authentication pages with a sidebar.
 */
export default function AuthLayout(props: {
  children: React.ReactNode;
}) {
  const defaultVariants: AuthLayoutProps = {
    containerVariant: 'default',
    wrapperVariant: 'default',
    sidebarVariant: 'default',
    textVariant: 'default',
  };

  const { containerVariant, wrapperVariant, sidebarVariant, textVariant } = defaultVariants;

  return (
    <div className={authLayoutContainerVariants({ variant: containerVariant })}>
      <ThemeProvider>
        <div className={authLayoutWrapperVariants({ variant: wrapperVariant })}>
          {props.children}
          <div className={authLayoutSidebarVariants({ variant: sidebarVariant })}>
            <div className="relative items-center justify-center flex z-1">
              <GridShape />
              <div className="flex flex-col items-center max-w-xs">
                <Link href="/" className="block mb-4">
                  <Image
                    width={150}
                    height={150}
                    src="/images/logo/minastik-full-ver.png"
                    alt="Logo"
                    className="inline-block"
                  />
                </Link>
                <p className={authLayoutTextVariants({ variant: textVariant })}>
                  Advanced AI-powered Project Management System
                </p>
              </div>
            </div>
          </div>

          {/* <div className="fixed bottom-6 right-6 z-50 hidden sm:block">
            <ThemeTogglerTwo />
          </div> */}
        </div>
      </ThemeProvider>
    </div>
  );
}
