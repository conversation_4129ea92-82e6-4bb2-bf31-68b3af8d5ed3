'use client';

import type { VariantProps } from 'class-variance-authority';
import type { FC } from 'react';
import React from 'react';
import { cn } from '@/shared/utils/utils';
import { inputFieldHintVariants, inputFieldVariants } from './input-field-variants';

export type InputProps = {
  type?: 'text' | 'number' | 'email' | 'password' | 'date' | 'time' | string;
  id?: string;
  name?: string;
  placeholder?: string;
  defaultValue?: string | number;
  value?: string | number;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  className?: string;
  min?: string;
  max?: string;
  step?: number;
  disabled?: boolean;
  success?: boolean;
  error?: boolean;
  hint?: string; // Optional hint text
  variant?: VariantProps<typeof inputFieldVariants>['variant'];
  classNameParent?: string;
  maxLength?: number | undefined;
  accept?: string | undefined;
};

/**
 * Input Component
 *
 * This component uses semantic color tokens from our theming system.
 * It provides a consistent input field with customizable states and hint text.
 */
const Input: FC<InputProps> = ({
  type = 'text',
  id,
  name,
  placeholder,
  defaultValue,
  value,
  onChange,
  className = '',
  min,
  max,
  step,
  disabled = false,
  success = false,
  error = false,
  hint,
  variant = 'default',
  classNameParent = '',
  maxLength = undefined,
  accept = undefined,
}) => {
  // Determine the variant based on state
  const inputVariant = error ? 'error' : success ? 'success' : variant;

  return (
    <div className={`relative ${classNameParent}`}>
      <input
        type={type}
        id={id}
        name={name}
        placeholder={placeholder}
        defaultValue={defaultValue}
        value={value}
        onChange={onChange}
        min={min}
        max={max}
        step={step}
        disabled={disabled}
        maxLength={maxLength}
        accept={accept}
        className={cn(
          inputFieldVariants({
            variant: inputVariant,
            disabled,
          }),
          className,
        )}
      />

      {/* Optional Hint Text */}
      {hint && (
        <p
          className={cn(
            inputFieldHintVariants({
              variant: inputVariant,
            }),
          )}
        >
          {hint}
        </p>
      )}
    </div>
  );
};

export default Input;
