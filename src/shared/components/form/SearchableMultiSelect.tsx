import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Search } from 'lucide-react';
import Checkbox from './input/Checkbox';
import { useTranslations } from 'next-intl';

type Option = {
  value: string;
  text: string;
  selected: boolean;
};

type SearchableMultiSelectProps = {
  label: string;
  options: Option[];
  defaultSelected?: Option[];
  onChange?: (selected: string[]) => void;
  onSearch?: (query: string) => void;
  disabled?: boolean;
  placeholder?: string;
  searchPlaceholder?: string;
  isLoading?: boolean;
  backgroundColor?: string;
  positionView?: string;
  isOpenSearchFnc?: (value: boolean) => void;
  isOpenSearch?: boolean;
};

const emptyArray: Option[] = [];

const SearchableMultiSelect: React.FC<SearchableMultiSelectProps> = ({
  label,
  options,
  defaultSelected = emptyArray,
  onChange,
  onSearch,
  disabled = false,
  placeholder = 'Select option',
  searchPlaceholder = 'Search...',
  isLoading = false,
  backgroundColor = '',
  positionView,
  isOpenSearch,
  isOpenSearchFnc,
}) => {
  const t = useTranslations('main');

  const defaultSelectedRef = useRef(defaultSelected);
  const [selectedOptions, setSelectedOptions] = useState<Option[]>(defaultSelectedRef.current);
  const [isOpen, setIsOpen] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  // Derive filtered options from options and search value
  const filteredOptions = useMemo(() => {
    if (searchValue === '') {
      return options;
    }
    return options.filter(option =>
      option.text.toLowerCase().includes(searchValue.toLowerCase()),
    );
  }, [options, searchValue]);
  const [position, setPosition] = useState({ top: 0, left: 0 });

  const [widthView, setWidthView] = useState(0);

  const searchInputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const boxRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (isOpenSearch !== undefined) {
      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setIsOpen(isOpenSearch);
    }
  }, [isOpenSearch]);

  useEffect(() => {
    if (positionView?.includes('fixed') && boxRef.current) {
      const rect = boxRef.current.getBoundingClientRect();

      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setWidthView(boxRef.current.offsetWidth);
      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setPosition({ top: rect.top + 50, left: rect.left });
    }
  }, [positionView, isOpen]);

  // Handle search input changes with debounce
  const handleSearchChange = useCallback((value: string) => {
    // Always set the search value, even if it's just spaces
    setSearchValue(value?.toLowerCase()?.trim());

    // Clear any existing timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // Set a new timeout to debounce the search
    searchTimeoutRef.current = setTimeout(() => {
      // If onSearch callback is provided, use it (API search)
      if (onSearch) {
        // Always pass the value as is, including spaces
        onSearch(value?.toLowerCase()?.trim());
      }
      // No need to filter locally as it's handled by the useMemo for filteredOptions
    }, 300); // 300ms debounce
  }, [onSearch]);
  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        if (isOpenSearchFnc) {
          isOpenSearchFnc(false);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const toggleDropdown = () => {
    if (disabled || isLoading) {
      return;
    }
    setIsOpen(prev => !prev);

    if (!isOpen) {
      if (isOpenSearchFnc) {
        isOpenSearchFnc(true);
      }
    }
    // setSearchValue('');
  };

  const handleSelect = (op: Option) => {
    const newSelectedOptions = selectedOptions.some(o => o.value === op.value)
      ? selectedOptions.filter(o => o.value !== op.value)
      : [...selectedOptions, op];

    setSelectedOptions(newSelectedOptions);
    if (onChange) {
      onChange(newSelectedOptions.map(o => o.value));
    }
  };

  // We don't need the removeOption function in the current implementation
  // since we're showing options as a comma-separated list without individual removal

  // const selectedValuesText = selectedOptions.map(
  //   value => options.find(option => option.value === value)?.text || '',
  // );

  // console.log(options, selectedOptions);

  return (
    <div className="w-full" ref={dropdownRef}>
      <label className="mb-1.5 block text-sm font-medium text-muted-foreground">
        {label}
      </label>

      <div className="relative z-20 inline-block w-full">
        <div className="relative flex flex-col items-center">
          <div
            ref={boxRef}
            onClick={toggleDropdown}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                e.preventDefault();
                toggleDropdown();
              }
            }}
            tabIndex={0}
            role="button"
            aria-expanded={isOpen}
            aria-haspopup="listbox"
            className="w-full "
          >
            <div className={`mb-2 flex h-11 rounded-lg border border-input py-1.5 pl-3 pr-3 shadow-theme-xs outline-hidden transition focus:border-primary/70 focus:shadow-focus-ring ${disabled ? backgroundColor : 'bg-background'}`}>
              <div className="flex flex-wrap flex-auto gap-2 overflow-hidden">
                {selectedOptions.length > 0
                  ? (
                      <div className="flex items-center w-full h-full p-1 overflow-hidden">
                        <div className="text-sm truncate">
                          {selectedOptions.map(o => o.text).join(', ')}
                        </div>
                      </div>
                    )
                  : (
                      <input
                        placeholder={placeholder ?? t('labelSelectOption')}
                        className="w-full h-full p-1 pr-2 text-sm bg-transparent border-0 outline-hidden appearance-none placeholder:text-muted-foreground focus:border-0 focus:outline-hidden focus:ring-0"
                        readOnly
                        value={isLoading ? t('loading') : placeholder}
                      />
                    )}
              </div>
              <div className="flex items-center py-1 pl-1 pr-1 w-7">
                <button
                  type="button"
                  onClick={toggleDropdown}
                  className="w-5 h-5 text-muted-foreground outline-hidden cursor-pointer focus:outline-hidden"
                  disabled={disabled || isLoading}
                >
                  <svg
                    className={`stroke-current ${isOpen ? 'rotate-180' : ''}`}
                    width="20"
                    height="20"
                    viewBox="0 0 20 20"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M4.79175 7.39551L10.0001 12.6038L15.2084 7.39551"
                      stroke="currentColor"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </button>
              </div>
            </div>
          </div>

          {isOpen && (
            <div
              className={`${positionView || 'absolute left-0 top-full w-full'}  z-40  overflow-y-auto bg-background rounded-lg shadow-sm max-h-60`}
              style={{
                ...(positionView?.includes('fixed') ? { top: `${position.top}px`, width: `${widthView}px` } : {}),
              }}
              onClick={e => e.stopPropagation()}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  e.stopPropagation();
                }
              }}
              tabIndex={0}
              role="listbox"
            >
              {/* Search input */}
              <div className="sticky top-0 p-2 bg-background border-b border-border z-1">
                <div className="relative">
                  <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <input
                    ref={searchInputRef}
                    type="text"
                    placeholder={searchPlaceholder}
                    value={searchValue}
                    onChange={e => handleSearchChange(e.target.value)}
                    onKeyDown={(e) => {
                      // Prevent default behavior for space key to allow typing spaces
                      if (e.key === ' ') {
                        e.stopPropagation();
                      }
                    }}
                    className="w-full pl-8 pr-2 py-2 text-sm border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-primary/70"
                    onClick={e => e.stopPropagation()}
                  />
                </div>
              </div>

              {/* Options list */}
              <div className="flex flex-col">
                {isLoading
                  ? (
                      <div className="p-4 text-center text-muted-foreground">{t('loading')}</div>
                    )
                  : filteredOptions.length === 0
                    ? (
                        <div className="p-4 text-center text-muted-foreground">{t('noResult')}</div>
                      )
                    : (
                        filteredOptions.map((option: Option, index: number) => (
                          <div key={index}>
                            <div
                              className="hover:bg-primary/5 w-full cursor-pointer border-b border-border"
                              onClick={() => handleSelect(option)}
                              onKeyDown={(e) => {
                                if (e.key === 'Enter' || e.key === ' ') {
                                  e.preventDefault();
                                  handleSelect(option);
                                }
                              }}
                              tabIndex={0}
                              role="option"
                              aria-selected={selectedOptions.some(o => o.value === option.value)}
                            >
                              <div
                                className={`relative flex w-full items-center p-2 pl-2 ${
                                  selectedOptions.some(o => o.value === option.value)
                                    ? 'bg-primary/10'
                                    : ''
                                }`}
                                role="none"
                              >
                                <div className="flex items-center w-full gap-2">
                                  <Checkbox
                                    checked={selectedOptions.some(o => o.value === option.value)}
                                    onChange={() => {
                                      // This will be triggered when clicking directly on the checkbox
                                      handleSelect(option);
                                    }}
                                    className="mr-3 flex-shrink-0"
                                  />
                                  <div className="leading-6 text-foreground">
                                    {option.text}
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))
                      )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SearchableMultiSelect;
